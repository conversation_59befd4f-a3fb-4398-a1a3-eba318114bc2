<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AutoSpace - Advanced Document Processing Dashboard</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: #0a0a0a;
            color: #ffffff;
            overflow-x: hidden;
            min-height: 100vh;
        }

        .dashboard-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
            min-height: 100vh;
        }

        .dashboard-card {
            background: rgba(30, 30, 30, 0.9);
            border-radius: 12px;
            padding: 24px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            border-color: rgba(102, 126, 234, 0.3);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .card-title {
            font-size: 0.8rem;
            font-weight: 500;
            color: #888;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .card-value {
            font-size: 3rem;
            font-weight: 700;
            color: #ffffff;
            font-family: 'JetBrains Mono', monospace;
        }

        .card-subtitle {
            font-size: 0.9rem;
            color: #666;
            margin-top: 8px;
        }

        .metric-large {
            font-size: 4rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .progress-ring {
            width: 120px;
            height: 120px;
            margin: 20px auto;
            position: relative;
        }

        .progress-ring svg {
            width: 100%;
            height: 100%;
            transform: rotate(-90deg);
        }

        .progress-ring circle {
            fill: none;
            stroke-width: 8;
        }

        .progress-ring .bg {
            stroke: rgba(255, 255, 255, 0.1);
        }

        .progress-ring .progress {
            stroke: #667eea;
            stroke-linecap: round;
            stroke-dasharray: 283;
            stroke-dashoffset: 70;
            transition: stroke-dashoffset 0.5s ease;
        }

        .progress-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }

        .progress-value {
            font-size: 1.8rem;
            font-weight: 700;
            color: #ffffff;
        }

        .progress-label {
            font-size: 0.7rem;
            color: #888;
            text-transform: uppercase;
        }

        .chart-container {
            height: 100px;
            display: flex;
            align-items: end;
            gap: 8px;
            margin: 20px 0;
        }

        .chart-bar {
            flex: 1;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .chart-bar.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .chart-bar:hover {
            background: rgba(102, 126, 234, 0.5);
        }

        .user-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
            margin: 20px 0;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .nav-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(10, 10, 10, 0.95);
            backdrop-filter: blur(20px);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            z-index: 1000;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .nav-logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: #667eea;
            text-decoration: none;
            font-family: 'JetBrains Mono', monospace;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            list-style: none;
        }

        .nav-links a {
            color: #ffffff;
            text-decoration: none;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .nav-links a:hover {
            background: rgba(102, 126, 234, 0.2);
            color: #667eea;
        }

        .cta-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .dashboard-container {
            margin-top: 80px;
        }

        .feature-highlight {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border: 1px solid rgba(102, 126, 234, 0.3);
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #00ff88;
            display: inline-block;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        @keyframes glow {
            0%, 100% { box-shadow: 0 0 20px rgba(102, 126, 234, 0.3); }
            50% { box-shadow: 0 0 30px rgba(102, 126, 234, 0.6); }
        }

        .geometric-bg {
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            opacity: 0.1;
            background: linear-gradient(45deg, #667eea, transparent);
            clip-path: polygon(0 0, 100% 0, 100% 70%, 30% 100%, 0 100%);
            animation: float 6s ease-in-out infinite;
        }

        .feature-highlight {
            animation: glow 3s ease-in-out infinite;
        }

        .metric-counter {
            display: inline-block;
            font-variant-numeric: tabular-nums;
        }

        @media (max-width: 768px) {
            .dashboard-container {
                grid-template-columns: 1fr;
                padding: 10px;
            }
            
            .nav-links {
                display: none;
            }
            
            .card-value {
                font-size: 2rem;
            }
            
            .metric-large {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="nav-header">
        <a href="/home" class="nav-logo">AUTOSPACE</a>
        <ul class="nav-links">
            <li><a href="/home">Dashboard</a></li>
            <li><a href="/about">About</a></li>
            <li><a href="/">Application</a></li>
        </ul>
        <a href="/login" class="cta-button">Get Started</a>
    </nav>

    <!-- Dashboard Container -->
    <div class="dashboard-container">
        <!-- Today's Focus -->
        <div class="dashboard-card feature-highlight">
            <div class="card-header">
                <span class="card-title">Today's Focus</span>
                <i class="fas fa-clock"></i>
            </div>
            <div class="card-value">1.8</div>
            <div class="card-subtitle">/ 6H</div>
            <div class="geometric-bg"></div>
        </div>

        <!-- Completed Tasks -->
        <div class="dashboard-card">
            <div class="card-header">
                <span class="card-title">Completed Tasks</span>
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="card-value">2</div>
            <div class="card-subtitle">/ 5</div>
        </div>

        <!-- Processing Stats -->
        <div class="dashboard-card">
            <div class="card-header">
                <span class="card-title">Paid Invoices</span>
                <i class="fas fa-file-invoice"></i>
            </div>
            <div class="card-value">24</div>
            <div class="card-subtitle">/ 32</div>
            <div style="margin-top: 10px; font-size: 0.8rem; color: #888;">
                TOTAL: $6,000 / 12,000
            </div>
        </div>

        <!-- Latest Work -->
        <div class="dashboard-card">
            <div class="card-header">
                <span class="card-title">Latest Work</span>
                <i class="fas fa-history"></i>
            </div>
            <div class="card-value">6.9</div>
            <div class="card-subtitle">/ 15H</div>
            <div class="user-grid">
                <div class="user-avatar">AB</div>
                <div class="user-avatar">CD</div>
                <div class="user-avatar">EF</div>
                <div class="user-avatar">GH</div>
                <div class="user-avatar">IJ</div>
                <div class="user-avatar">KL</div>
            </div>
        </div>

        <!-- Total Balance -->
        <div class="dashboard-card">
            <div class="card-header">
                <span class="card-title">Total Balance (BTC)</span>
                <div>
                    <span style="font-size: 0.7rem; color: #888;">7D</span>
                    <span style="font-size: 0.7rem; color: #888; margin-left: 10px;">30D</span>
                    <span style="font-size: 0.7rem; color: #667eea; margin-left: 10px;">5M</span>
                    <span style="font-size: 0.7rem; color: #888; margin-left: 10px;">1Y</span>
                </div>
            </div>
            <div class="metric-large">1.592</div>
            <div class="chart-container">
                <div class="chart-bar" style="height: 40%;"></div>
                <div class="chart-bar" style="height: 60%;"></div>
                <div class="chart-bar active" style="height: 100%;"></div>
                <div class="chart-bar" style="height: 80%;"></div>
                <div class="chart-bar" style="height: 70%;"></div>
            </div>
        </div>

        <!-- Work-Life Balance -->
        <div class="dashboard-card">
            <div class="card-header">
                <span class="card-title">Processing Efficiency</span>
                <i class="fas fa-chart-pie"></i>
            </div>
            <div class="progress-ring">
                <svg>
                    <circle class="bg" cx="60" cy="60" r="45"></circle>
                    <circle class="progress" cx="60" cy="60" r="45"></circle>
                </svg>
                <div class="progress-center">
                    <div class="progress-value">7.89</div>
                    <div class="progress-label">EFFICIENCY</div>
                </div>
            </div>
        </div>

        <!-- AutoSpace Features -->
        <div class="dashboard-card feature-highlight">
            <div class="card-header">
                <span class="card-title">AutoSpace Features</span>
                <i class="fas fa-robot"></i>
            </div>
            <div style="margin: 20px 0;">
                <h3 style="color: #667eea; font-size: 1.2rem; margin-bottom: 10px;">DOCUMENT PROCESSING</h3>
                <p style="color: #888; font-size: 0.9rem; line-height: 1.5;">
                    Advanced AI-powered document extraction, comparison, and analysis tools for maximum productivity.
                </p>
                <div style="margin-top: 15px;">
                    <span style="color: #00ff88; font-size: 0.8rem;">
                        <span class="status-indicator"></span>ACTIVE
                    </span>
                </div>
            </div>
        </div>

        <!-- API Usage -->
        <div class="dashboard-card">
            <div class="card-header">
                <span class="card-title">ChatGPT API Usage</span>
                <i class="fas fa-brain"></i>
            </div>
            <div class="card-value">5.01</div>
            <div class="card-subtitle">/ $10.00</div>
        </div>

        <!-- Real-time Status -->
        <div class="dashboard-card">
            <div class="card-header">
                <span class="card-title">System Status</span>
                <i class="fas fa-server"></i>
            </div>
            <div style="text-align: center; margin: 20px 0;">
                <div class="real-time-clock" style="font-size: 1.5rem; font-weight: 600; color: #667eea; font-family: 'JetBrains Mono', monospace;">--:--:--</div>
                <div style="margin-top: 10px;">
                    <span class="status-indicator"></span>
                    <span style="color: #00ff88; font-size: 0.8rem;">ALL SYSTEMS OPERATIONAL</span>
                </div>
            </div>
        </div>

        <!-- Custom Dashboard -->
        <div class="dashboard-card" style="background: rgba(200, 200, 200, 0.1);">
            <div class="card-header">
                <span class="card-title" style="color: #333;">Custom Dashboard</span>
                <i class="fas fa-cog" style="color: #333;"></i>
            </div>
            <div style="text-align: right; margin-top: 40px;">
                <span style="color: #333; font-size: 0.8rem;">10/ 20 TEMPLATES</span>
            </div>
        </div>
    </div>

    <script>
        // Counter animation function
        function animateCounter(element, target, duration = 2000) {
            const start = 0;
            const increment = target / (duration / 16);
            let current = start;

            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }

                if (target % 1 === 0) {
                    element.textContent = Math.floor(current);
                } else {
                    element.textContent = current.toFixed(2);
                }
            }, 16);
        }

        // Animate progress ring
        document.addEventListener('DOMContentLoaded', function() {
            const progressRing = document.querySelector('.progress-ring .progress');
            const percentage = 75; // 7.89/10 * 100
            const circumference = 2 * Math.PI * 45;
            const offset = circumference - (percentage / 100) * circumference;

            setTimeout(() => {
                progressRing.style.strokeDashoffset = offset;
            }, 500);

            // Animate counters
            setTimeout(() => {
                const counters = [
                    { element: document.querySelector('.dashboard-card:nth-child(1) .card-value'), target: 1.8 },
                    { element: document.querySelector('.dashboard-card:nth-child(2) .card-value'), target: 2 },
                    { element: document.querySelector('.dashboard-card:nth-child(3) .card-value'), target: 24 },
                    { element: document.querySelector('.dashboard-card:nth-child(4) .card-value'), target: 6.9 },
                    { element: document.querySelector('.dashboard-card:nth-child(5) .metric-large'), target: 1.592 },
                    { element: document.querySelector('.dashboard-card:nth-child(7) .progress-value'), target: 7.89 },
                    { element: document.querySelector('.dashboard-card:nth-child(9) .card-value'), target: 5.01 }
                ];

                counters.forEach((counter, index) => {
                    if (counter.element) {
                        counter.element.textContent = '0';
                        setTimeout(() => {
                            animateCounter(counter.element, counter.target);
                        }, index * 200);
                    }
                });
            }, 1000);
        });

        // Add hover effects to cards
        document.querySelectorAll('.dashboard-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px) scale(1.02)';
                this.style.transition = 'all 0.3s ease';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Animate chart bars on load
        setTimeout(() => {
            document.querySelectorAll('.chart-bar').forEach((bar, index) => {
                bar.style.transition = 'height 0.8s ease';
                bar.style.animationDelay = `${index * 0.1}s`;

                // Add click interaction
                bar.addEventListener('click', function() {
                    this.style.background = 'linear-gradient(135deg, #667eea, #764ba2)';
                    setTimeout(() => {
                        this.style.background = this.classList.contains('active') ?
                            'linear-gradient(135deg, #667eea, #764ba2)' :
                            'rgba(255, 255, 255, 0.1)';
                    }, 300);
                });
            });
        }, 1500);

        // Real-time clock update
        function updateClock() {
            const now = new Date();
            const timeString = now.toLocaleTimeString();
            const clockElement = document.querySelector('.real-time-clock');
            if (clockElement) {
                clockElement.textContent = timeString;
            }
        }

        setInterval(updateClock, 1000);
        updateClock();

        // Simulate real-time data updates
        setInterval(() => {
            const randomCard = document.querySelectorAll('.dashboard-card')[Math.floor(Math.random() * 9)];
            randomCard.style.borderColor = 'rgba(102, 126, 234, 0.5)';
            setTimeout(() => {
                randomCard.style.borderColor = 'rgba(255, 255, 255, 0.1)';
            }, 1000);
        }, 5000);
    </script>
</body>
</html>
